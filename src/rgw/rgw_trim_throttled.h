// -*- mode:C++; tab-width:8; c-basic-offset:2; indent-tabs-mode:t -*-
// vim: ts=8 sw=2 smarttab ft=cpp

/*
 * Ceph - scalable distributed file system
 *
 * Copyright (C) 2024 Red Hat, Inc.
 *
 * This is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License version 2.1, as published by the Free Software
 * Foundation. See file COPYING.
 *
 */

#pragma once

#include "rgw_coroutine.h"
#include "rgw_sync.h"
#include "rgw_trim_throttle.h"
#include "rgw_trim_mdlog.h"
#include "rgw_trim_datalog.h"
#include "rgw_trim_bilog.h"
#include "rgw_worker.h"
#include "common/ceph_time.h"

class RGWCoroutinesManager;
class RGWHTTPManager;
class RGWSyncProcessorThread;

class DoutPrefixProvider;

namespace rgw {

/**
 * Throttled Metadata Log Trim Coroutine
 * Integrates dynamic throttling into metadata log trimming
 */
class ThrottledMetaTrimCR : public RGWCoroutine {
  rgw::sal::RadosStore* store;
  std::shared_ptr<TrimThrottleManager> throttle_manager;
  RGWHTTPManager* http;
  int num_shards;
  const DoutPrefixProvider* dpp;

public:
  ThrottledMetaTrimCR(const DoutPrefixProvider* dpp,
                      rgw::sal::RadosStore* store,
                      std::shared_ptr<TrimThrottleManager> throttle_manager,
                      RGWHTTPManager* http,
                      int num_shards);

  int operate(const DoutPrefixProvider* dpp) override;

private:
  int trim_with_throttling(const DoutPrefixProvider* dpp);
};

/**
 * Throttled Data Log Trim Coroutine
 * Integrates dynamic throttling into data log trimming
 */
class ThrottledDataTrimCR : public RGWCoroutine {
  rgw::sal::RadosStore* store;
  std::shared_ptr<TrimThrottleManager> throttle_manager;
  RGWHTTPManager* http;
  int num_shards;
  const DoutPrefixProvider* dpp;

public:
  ThrottledDataTrimCR(const DoutPrefixProvider* dpp,
                      rgw::sal::RadosStore* store,
                      std::shared_ptr<TrimThrottleManager> throttle_manager,
                      RGWHTTPManager* http,
                      int num_shards);

  int operate(const DoutPrefixProvider* dpp) override;

private:
  int trim_with_throttling(const DoutPrefixProvider* dpp);
};

/**
 * Throttled Bucket Index Log Trim Coroutine
 * Integrates dynamic throttling into bucket index log trimming
 */
class ThrottledBucketTrimCR : public RGWCoroutine {
  rgw::sal::RadosStore* store;
  std::shared_ptr<TrimThrottleManager> throttle_manager;
  RGWHTTPManager* http;
  const DoutPrefixProvider* dpp;

public:
  ThrottledBucketTrimCR(const DoutPrefixProvider* dpp,
                        rgw::sal::RadosStore* store,
                        std::shared_ptr<TrimThrottleManager> throttle_manager,
                        RGWHTTPManager* http);

  int operate(const DoutPrefixProvider* dpp) override;

private:
  int trim_with_throttling(const DoutPrefixProvider* dpp);
};

/**
 * Throttled Trim Manager
 * Coordinates all throttled trim operations
 */
class ThrottledTrimManager {
public:
  explicit ThrottledTrimManager(CephContext* cct, rgw::sal::RadosStore* store);
  ~ThrottledTrimManager();

  // Initialize with configuration
  int init(const TrimThrottleConfig& config = {});
  void shutdown();

  // Create throttled trim coroutines
  RGWCoroutine* create_meta_trim_cr(const DoutPrefixProvider* dpp,
                                    RGWHTTPManager* http,
                                    int num_shards);

  RGWCoroutine* create_data_trim_cr(const DoutPrefixProvider* dpp,
                                    RGWHTTPManager* http,
                                    int num_shards);

  RGWCoroutine* create_bucket_trim_cr(const DoutPrefixProvider* dpp,
                                      RGWHTTPManager* http);

  // Get throttle manager for direct access
  std::shared_ptr<TrimThrottleManager> get_throttle_manager() const {
    return throttle_manager;
  }

  // Configuration management
  void update_config(const TrimThrottleConfig& config);
  TrimThrottleConfig get_config() const;

  // Status queries
  bool is_overloaded() const;
  uint32_t get_concurrency_limit() const;

private:
  CephContext* cct;
  rgw::sal::RadosStore* store;
  std::shared_ptr<TrimThrottleManager> throttle_manager;
  std::atomic<bool> initialized{false};
};

/**
 * Throttled Shard Collector
 * A specialized shard collector that respects throttling limits
 */
class ThrottledShardCollectCR : public RGWShardCollectCR {
  std::shared_ptr<TrimThrottleManager> throttle_manager;
  const DoutPrefixProvider* dpp;

public:
  ThrottledShardCollectCR(CephContext* cct,
                          std::shared_ptr<TrimThrottleManager> throttle_manager,
                          const DoutPrefixProvider* dpp);

protected:
  // Override to implement throttling
  bool spawn_next() override;
};

/**
 * Batch Trim Operation
 * Template for batched trim operations with throttling
 */
template<typename ItemType>
class BatchTrimOperation {
public:
  using TrimFunc = std::function<int(const std::vector<ItemType>&, const DoutPrefixProvider*)>;

  BatchTrimOperation(std::shared_ptr<TrimThrottleManager> throttle_manager,
                     TrimFunc trim_func);

  // Execute trim operation with batching and throttling
  int execute(const std::vector<ItemType>& items, const DoutPrefixProvider* dpp);

private:
  std::shared_ptr<TrimThrottleManager> throttle_manager;
  TrimFunc trim_func;
};

// Factory functions for creating throttled trim coroutines
RGWCoroutine* create_throttled_meta_log_trim_cr(const DoutPrefixProvider* dpp,
                                                rgw::sal::RadosStore* store,
                                                std::shared_ptr<ThrottledTrimManager> manager,
                                                RGWHTTPManager* http,
                                                int num_shards);

RGWCoroutine* create_throttled_data_log_trim_cr(const DoutPrefixProvider* dpp,
                                                rgw::sal::RadosStore* store,
                                                std::shared_ptr<ThrottledTrimManager> manager,
                                                RGWHTTPManager* http,
                                                int num_shards);

RGWCoroutine* create_throttled_bucket_log_trim_cr(const DoutPrefixProvider* dpp,
                                                  rgw::sal::RadosStore* store,
                                                  std::shared_ptr<ThrottledTrimManager> manager,
                                                  RGWHTTPManager* http);

/**
 * Throttled Sync Log Trim Thread
 * Replaces the original RGWSyncLogTrimThread with throttling capabilities
 */
class RGWThrottledSyncLogTrimThread : public RGWRadosThread, public DoutPrefixProvider {
  RGWCoroutinesManager crs;
  rgw::sal::RadosStore* store;
  rgw::BucketTrimManager* bucket_trim;
  RGWHTTPManager http;
  const utime_t trim_interval;
  std::shared_ptr<ThrottledTrimManager> throttled_trim_manager;

  uint64_t interval_msec() override { return 0; }
  void stop_process() override { crs.stop(); }

public:
  RGWThrottledSyncLogTrimThread(rgw::sal::RadosStore* store,
                                rgw::BucketTrimManager* bucket_trim,
                                int interval);

  int init(const DoutPrefixProvider *dpp) override;
  void stop();

  // DoutPrefixProvider implementation
  CephContext *get_cct() const override { return store->ctx(); }
  unsigned get_subsys() const override { return dout_subsys; }
  std::ostream& gen_prefix(std::ostream& out) const override;

private:
  int process(const DoutPrefixProvider *dpp) override;
};

} // namespace rgw
