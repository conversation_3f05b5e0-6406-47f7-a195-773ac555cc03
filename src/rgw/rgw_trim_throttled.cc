// -*- mode:C++; tab-width:8; c-basic-offset:2; indent-tabs-mode:t -*-
// vim: ts=8 sw=2 smarttab ft=cpp

/*
 * Ceph - scalable distributed file system
 *
 * Copyright (C) 2024 Red Hat, Inc.
 *
 * This is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License version 2.1, as published by the Free Software
 * Foundation. See file COPYING.
 *
 */

#include "rgw_trim_throttled.h"
#include "rgw_trim_batched.h"

#include "common/dout.h"
#include "common/errno.h"
#include "rgw_sal_rados.h"
#include "rgw_coroutine.h"
#include "rgw_worker.h"
#include "rgw_zone.h"
#include "services/svc_zone.h"

#include <boost/asio.hpp>
#include <boost/asio/coroutine.hpp>
#include <boost/asio/yield.hpp>

#define dout_context g_ceph_context
#define dout_subsys ceph_subsys_rgw
#undef dout_prefix
#define dout_prefix (*_dout << "rgw throttled trim: ")

namespace rgw {

// ThrottledMetaTrimCR implementation
ThrottledMetaTrimCR::ThrottledMetaTrimCR(const DoutPrefixProvider* dpp,
                                         rgw::sal::RadosStore* store,
                                         std::shared_ptr<TrimThrottleManager> throttle_manager,
                                         RGWHTTPManager* http,
                                         int num_shards)
  : RGWCoroutine(store->ctx()), store(store), throttle_manager(throttle_manager),
    http(http), num_shards(num_shards), dpp(dpp) {
}

int ThrottledMetaTrimCR::operate(const DoutPrefixProvider* dpp) {
  BOOST_ASIO_CORO_REENTER(this) {
    ldpp_dout(dpp, 10) << __func__ << ": starting throttled metadata trim" << dendl;
    
    // Check if system is overloaded
    if (throttle_manager->is_overloaded()) {
      ldpp_dout(dpp, 10) << __func__ << ": system overloaded, delaying trim" << dendl;
      auto delay = throttle_manager->get_batch_delay();
      wait(utime_t(delay.count() / 1000, (delay.count() % 1000) * 1000000));
    }
    
    yield return trim_with_throttling(dpp);
    
    ldpp_dout(dpp, 10) << __func__ << ": throttled metadata trim completed" << dendl;
    return set_cr_done();
  }
  return 0;
}

int ThrottledMetaTrimCR::trim_with_throttling(const DoutPrefixProvider* dpp) {
  // 实现真正的分批 metadata trim
  ldpp_dout(dpp, 10) << __func__ << ": starting throttled metadata trim with batching" << dendl;

  uint32_t concurrency = throttle_manager->get_concurrency_limit();
  uint32_t batch_size = throttle_manager->get_controller()->get_batch_size();

  ldpp_dout(dpp, 15) << __func__ << ": using concurrency=" << concurrency
                     << " batch_size=" << batch_size << dendl;

  // 创建分批处理的 metadata trim 协程
  auto batch_trim_cr = create_batched_meta_trim_cr(dpp, store, http, num_shards,
                                                   concurrency, batch_size);
  if (!batch_trim_cr) {
    return -EINVAL;
  }

  call(batch_trim_cr);
  return 0;
}

// ThrottledDataTrimCR implementation
ThrottledDataTrimCR::ThrottledDataTrimCR(const DoutPrefixProvider* dpp,
                                         rgw::sal::RadosStore* store,
                                         std::shared_ptr<TrimThrottleManager> throttle_manager,
                                         RGWHTTPManager* http,
                                         int num_shards)
  : RGWCoroutine(store->ctx()), store(store), throttle_manager(throttle_manager),
    http(http), num_shards(num_shards), dpp(dpp) {
}

int ThrottledDataTrimCR::operate(const DoutPrefixProvider* dpp) {
  BOOST_ASIO_CORO_REENTER(this) {
    ldpp_dout(dpp, 10) << __func__ << ": starting throttled data trim" << dendl;
    
    // Check if system is overloaded
    if (throttle_manager->is_overloaded()) {
      ldpp_dout(dpp, 10) << __func__ << ": system overloaded, delaying trim" << dendl;
      auto delay = throttle_manager->get_batch_delay();
      wait(utime_t(delay.count() / 1000, (delay.count() % 1000) * 1000000));
    }
    
    yield return trim_with_throttling(dpp);
    
    ldpp_dout(dpp, 10) << __func__ << ": throttled data trim completed" << dendl;
    return set_cr_done();
  }
  return 0;
}

int ThrottledDataTrimCR::trim_with_throttling(const DoutPrefixProvider* dpp) {
  // 实现真正的分批 datalog trim
  ldpp_dout(dpp, 10) << __func__ << ": starting throttled datalog trim with batching" << dendl;

  uint32_t concurrency = throttle_manager->get_concurrency_limit();
  uint32_t batch_size = throttle_manager->get_controller()->get_batch_size();

  ldpp_dout(dpp, 15) << __func__ << ": using concurrency=" << concurrency
                     << " batch_size=" << batch_size << dendl;

  // 创建分批处理的 datalog trim 协程
  auto batch_trim_cr = create_batched_data_trim_cr(dpp, store, http, num_shards,
                                                   concurrency, batch_size);
  if (!batch_trim_cr) {
    return -EINVAL;
  }

  call(batch_trim_cr);
  return 0;
}

// ThrottledBucketTrimCR implementation
ThrottledBucketTrimCR::ThrottledBucketTrimCR(const DoutPrefixProvider* dpp,
                                             rgw::sal::RadosStore* store,
                                             std::shared_ptr<TrimThrottleManager> throttle_manager,
                                             RGWHTTPManager* http)
  : RGWCoroutine(store->ctx()), store(store), throttle_manager(throttle_manager),
    http(http), dpp(dpp) {
}

int ThrottledBucketTrimCR::operate(const DoutPrefixProvider* dpp) {
  BOOST_ASIO_CORO_REENTER(this) {
    ldpp_dout(dpp, 10) << __func__ << ": starting throttled bucket trim" << dendl;
    
    // Check if system is overloaded
    if (throttle_manager->is_overloaded()) {
      ldpp_dout(dpp, 10) << __func__ << ": system overloaded, delaying trim" << dendl;
      auto delay = throttle_manager->get_batch_delay();
      wait(utime_t(delay.count() / 1000, (delay.count() % 1000) * 1000000));
    }
    
    yield return trim_with_throttling(dpp);
    
    ldpp_dout(dpp, 10) << __func__ << ": throttled bucket trim completed" << dendl;
    return set_cr_done();
  }
  return 0;
}

int ThrottledBucketTrimCR::trim_with_throttling(const DoutPrefixProvider* dpp) {
  // For bucket trim, we need to create a bucket trim manager and get its coroutine
  // This is a simplified implementation
  ldpp_dout(dpp, 15) << __func__ << ": executing bucket trim with throttling" << dendl;
  
  uint32_t concurrency = throttle_manager->get_concurrency_limit();
  ldpp_dout(dpp, 15) << __func__ << ": using concurrency limit " << concurrency << dendl;
  
  // TODO: Implement actual bucket trim with throttling
  // For now, just return success
  return 0;
}

// ThrottledTrimManager implementation
ThrottledTrimManager::ThrottledTrimManager(CephContext* cct, rgw::sal::RadosStore* store)
  : cct(cct), store(store) {
}

ThrottledTrimManager::~ThrottledTrimManager() {
  shutdown();
}

int ThrottledTrimManager::init(const TrimThrottleConfig& config) {
  if (initialized.exchange(true)) {
    return 0; // Already initialized
  }
  
  dout(10) << __func__ << ": initializing throttled trim manager" << dendl;
  
  // Create the underlying throttle manager
  throttle_manager = std::make_shared<TrimThrottleManager>(cct, store);
  int r = throttle_manager->init(config);
  if (r < 0) {
    derr << __func__ << ": failed to initialize throttle manager: " << cpp_strerror(r) << dendl;
    initialized = false;
    return r;
  }
  
  dout(10) << __func__ << ": throttled trim manager initialized successfully" << dendl;
  return 0;
}

void ThrottledTrimManager::shutdown() {
  if (!initialized.exchange(false)) {
    return; // Already shutdown
  }
  
  dout(10) << __func__ << ": shutting down throttled trim manager" << dendl;
  
  if (throttle_manager) {
    throttle_manager->shutdown();
    throttle_manager.reset();
  }
  
  dout(10) << __func__ << ": throttled trim manager shutdown complete" << dendl;
}

RGWCoroutine* ThrottledTrimManager::create_meta_trim_cr(const DoutPrefixProvider* dpp,
                                                        RGWHTTPManager* http,
                                                        int num_shards) {
  if (!throttle_manager) {
    return nullptr;
  }
  
  return new ThrottledMetaTrimCR(dpp, store, throttle_manager, http, num_shards);
}

RGWCoroutine* ThrottledTrimManager::create_data_trim_cr(const DoutPrefixProvider* dpp,
                                                        RGWHTTPManager* http,
                                                        int num_shards) {
  if (!throttle_manager) {
    return nullptr;
  }
  
  return new ThrottledDataTrimCR(dpp, store, throttle_manager, http, num_shards);
}

RGWCoroutine* ThrottledTrimManager::create_bucket_trim_cr(const DoutPrefixProvider* dpp,
                                                          RGWHTTPManager* http) {
  if (!throttle_manager) {
    return nullptr;
  }
  
  return new ThrottledBucketTrimCR(dpp, store, throttle_manager, http);
}

void ThrottledTrimManager::update_config(const TrimThrottleConfig& config) {
  if (throttle_manager) {
    throttle_manager->get_controller()->update_config(config);
  }
}

TrimThrottleConfig ThrottledTrimManager::get_config() const {
  if (throttle_manager) {
    return throttle_manager->get_controller()->get_config();
  }
  return {};
}

bool ThrottledTrimManager::is_overloaded() const {
  return throttle_manager ? throttle_manager->is_overloaded() : false;
}

uint32_t ThrottledTrimManager::get_concurrency_limit() const {
  return throttle_manager ? throttle_manager->get_concurrency_limit() : 1;
}

// ThrottledShardCollectCR implementation
ThrottledShardCollectCR::ThrottledShardCollectCR(CephContext* cct,
                                                 std::shared_ptr<TrimThrottleManager> throttle_manager,
                                                 const DoutPrefixProvider* dpp)
  : RGWShardCollectCR(cct, throttle_manager->get_concurrency_limit()),
    throttle_manager(throttle_manager), dpp(dpp) {
}

bool ThrottledShardCollectCR::spawn_next() {
  // Check if we're within concurrency limits and system isn't overloaded
  if (throttle_manager->is_overloaded()) {
    ldpp_dout(dpp, 15) << __func__ << ": system overloaded, not spawning" << dendl;
    return false;
  }

  uint32_t current_limit = throttle_manager->get_concurrency_limit();
  if (num_spawned() >= current_limit) {
    ldpp_dout(dpp, 15) << __func__ << ": concurrency limit reached ("
                       << current_limit << "), not spawning" << dendl;
    return false;
  }

  // Add delay between spawns if system is under load
  auto delay = throttle_manager->get_batch_delay();
  if (delay.count() > 0) {
    ldpp_dout(dpp, 20) << __func__ << ": adding delay of "
                       << delay.count() << "ms between spawns" << dendl;
    std::this_thread::sleep_for(delay);
  }

  // This method should be overridden by derived classes to actually spawn work
  return false;
}

// Factory functions
RGWCoroutine* create_throttled_meta_log_trim_cr(const DoutPrefixProvider* dpp,
                                                rgw::sal::RadosStore* store,
                                                std::shared_ptr<ThrottledTrimManager> manager,
                                                RGWHTTPManager* http,
                                                int num_shards) {
  return manager->create_meta_trim_cr(dpp, http, num_shards);
}

RGWCoroutine* create_throttled_data_log_trim_cr(const DoutPrefixProvider* dpp,
                                                rgw::sal::RadosStore* store,
                                                std::shared_ptr<ThrottledTrimManager> manager,
                                                RGWHTTPManager* http,
                                                int num_shards) {
  return manager->create_data_trim_cr(dpp, http, num_shards);
}

RGWCoroutine* create_throttled_bucket_log_trim_cr(const DoutPrefixProvider* dpp,
                                                  rgw::sal::RadosStore* store,
                                                  std::shared_ptr<ThrottledTrimManager> manager,
                                                  RGWHTTPManager* http) {
  return manager->create_bucket_trim_cr(dpp, http);
}

/**
 * Throttled Sync Log Trim Thread Implementation
 */

RGWThrottledSyncLogTrimThread::RGWThrottledSyncLogTrimThread(rgw::sal::RadosStore* store,
                                                            rgw::BucketTrimManager* bucket_trim,
                                                            int interval)
  : RGWRadosThread(store->getRados(), "throttled-sync-log-trim"),
    crs(store->ctx(), store->getRados()->get_cr_registry()),
    store(store),
    bucket_trim(bucket_trim),
    http(store->ctx(), crs.get_completion_mgr()),
    trim_interval(interval, 0) {

  // Create throttled trim manager
  throttled_trim_manager = std::make_shared<ThrottledTrimManager>(store->ctx(), store);
}

int RGWThrottledSyncLogTrimThread::init(const DoutPrefixProvider* dpp) {
    int r = http.start();
    if (r < 0) {
      return r;
    }

    // Initialize throttled trim manager if throttling is enabled
    if (cct->_conf.get_val<bool>("rgw_trim_throttle_enable")) {
      auto config = TrimThrottleConfigHelper::load_from_ceph_context(cct);
      r = throttled_trim_manager->init(config);
      if (r < 0) {
        ldpp_dout(dpp, 1) << "failed to initialize throttled trim manager: "
                          << cpp_strerror(r) << dendl;
        // Continue without throttling
      } else {
        ldpp_dout(dpp, 10) << "throttled trim manager initialized successfully" << dendl;
      }
    }

    return 0;
  }

int RGWThrottledSyncLogTrimThread::process(const DoutPrefixProvider* dpp) {
    std::list<RGWCoroutinesStack*> stacks;

    // Check if throttling is enabled and manager is initialized
    bool use_throttling = cct->_conf.get_val<bool>("rgw_trim_throttle_enable") &&
                          throttled_trim_manager;

    if (use_throttling) {
      ldpp_dout(dpp, 10) << "using throttled trim operations" << dendl;

      // Create throttled metadata trim coroutine
      auto metatrimcr = throttled_trim_manager->create_meta_trim_cr(
        dpp, &http, cct->_conf->rgw_md_log_max_shards);

      if (metatrimcr) {
        auto meta = new RGWCoroutinesStack(store->ctx(), &crs);
        meta->call(metatrimcr);
        stacks.push_back(meta);
      } else {
        ldpp_dout(dpp, 1) << "failed to create throttled meta trim coroutine" << dendl;
      }

      // Create throttled data trim coroutine if needed
      if (store->svc()->zone->sync_module_exports_data()) {
        auto datatrimcr = throttled_trim_manager->create_data_trim_cr(
          dpp, &http, cct->_conf->rgw_data_log_num_shards);

        if (datatrimcr) {
          auto data = new RGWCoroutinesStack(store->ctx(), &crs);
          data->call(datatrimcr);
          stacks.push_back(data);
        }

        // Create throttled bucket trim coroutine
        auto buckettrimcr = throttled_trim_manager->create_bucket_trim_cr(dpp, &http);
        if (buckettrimcr) {
          auto bucket = new RGWCoroutinesStack(store->ctx(), &crs);
          bucket->call(buckettrimcr);
          stacks.push_back(bucket);
        }
      }
    } else {
      ldpp_dout(dpp, 10) << "using standard trim operations (throttling disabled)" << dendl;

      // Fall back to original trim implementation
      auto metatrimcr = create_meta_log_trim_cr(this, store, &http,
                                                cct->_conf->rgw_md_log_max_shards,
                                                trim_interval);
      if (!metatrimcr) {
        ldpp_dout(dpp, -1) << "failed to create meta log trim coroutine" << dendl;
        return -EINVAL;
      }
      auto meta = new RGWCoroutinesStack(store->ctx(), &crs);
      meta->call(metatrimcr);
      stacks.push_back(meta);

      if (store->svc()->zone->sync_module_exports_data()) {
        auto data = new RGWCoroutinesStack(store->ctx(), &crs);
        data->call(create_data_log_trim_cr(dpp, store, &http,
                                           cct->_conf->rgw_data_log_num_shards,
                                           trim_interval));
        stacks.push_back(data);

        auto bucket = new RGWCoroutinesStack(store->ctx(), &crs);
        bucket->call(bucket_trim->create_bucket_trim_cr(&http));
        stacks.push_back(bucket);
      }
    }

    if (stacks.empty()) {
      ldpp_dout(dpp, 1) << "no trim coroutines created" << dendl;
      return -EINVAL;
    }

    crs.run(dpp, stacks);
    return 0;
  }

void RGWThrottledSyncLogTrimThread::stop() {
  down_flag = true;
  stop_process();
}

std::ostream& RGWThrottledSyncLogTrimThread::gen_prefix(std::ostream& out) const {
  return out << "throttled sync log trim: ";
}

} // namespace rgw
