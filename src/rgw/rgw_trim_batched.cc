// -*- mode:C++; tab-width:8; c-basic-offset:2; indent-tabs-mode:t -*-
// vim: ts=8 sw=2 smarttab ft=cpp

/*
 * Ceph - scalable distributed file system
 *
 * Copyright (C) 2024 Red Hat, Inc.
 *
 * This is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License version 2.1, as published by the Free Software
 * Foundation. See file COPYING.
 *
 */

#include "rgw_trim_batched.h"

#include <thread>
#include <chrono>

#include "common/dout.h"
#include "common/errno.h"
#include "rgw_sal_rados.h"
#include "rgw_mdlog.h"
#include "rgw_datalog.h"
#include "services/svc_mdlog.h"
#include "services/svc_zone.h"
#include "rgw_zone.h"
#include "cls/log/cls_log_types.h"
#include "rgw_rados.h"
#include "rgw_http_client.h"
#include "include/rados/librados.hpp"
#include "rgw_rest_conn.h"
#include "rgw_cr_rest.h"
#include "rgw_data_sync.h"
#include "rgw_bucket.h"
#include "services/svc_bilog_rados.h"
#include <thread>

#define dout_subsys ceph_subsys_rgw
#undef dout_prefix
#define dout_prefix (*_dout << "rgw batched trim: ")

using namespace std::chrono_literals;

namespace {

/// return the marker that it's safe to trim up to
const std::string& get_stable_marker(const rgw_data_sync_marker& m)
{
  return m.state == m.FullSync ? m.next_step_marker : m.marker;
}

/// populate the container starting with 'dest' with the minimum stable marker
/// of each shard for all of the peers in [first, last)
template <typename IterIn, typename IterOut>
void take_min_markers(IterIn first, IterIn last, IterOut dest)
{
  if (first == last) {
    return;
  }
  for (auto p = first; p != last; ++p) {
    auto m = dest;
    for (auto &shard : p->sync_markers) {
      const auto& stable = get_stable_marker(shard.second);
      if (*m > stable) {
        *m = stable;
      }
      ++m;
    }
  }
}

/// Query all peers for their sync status and return the safe marker for a specific shard
int get_safe_datalog_marker(const DoutPrefixProvider* dpp,
                            rgw::sal::RadosStore* store,
                            int shard_id,
                            std::string& safe_marker)
{
  // Get zone information
  auto zone_id = store->svc()->zone->zone_id();
  auto& notify_map = store->svc()->zone->get_zone_data_notify_to_map();

  if (notify_map.empty()) {
    ldpp_dout(dpp, 10) << __func__ << ": no peers configured, using fallback strategy" << dendl;
    // 如果没有配置 peer，使用保守的时间策略
    // 只删除2小时前的条目
    safe_marker = ""; // 空 marker 表示不进行 trim
    return 0;
  }

  // 创建临时的协程管理器来查询 peer 状态
  RGWCoroutinesManager crs(store->ctx(), store->getRados()->get_cr_registry());
  RGWHTTPManager http_manager(store->ctx(), crs.get_completion_mgr());

  int ret = http_manager.start();
  if (ret < 0) {
    ldpp_dout(dpp, 1) << __func__ << ": failed to start http manager: " << cpp_strerror(ret) << dendl;
    return ret;
  }

  // 查询所有 peer 的同步状态
  std::vector<rgw_data_sync_status> peer_status(notify_map.size());

  // 创建查询参数
  std::string zone_id_str = zone_id.id;
  rgw_http_param_pair params[] = {
    { "type", "data" },
    { "status", nullptr },
    { "source-zone", zone_id_str.c_str() },
    { nullptr, nullptr }
  };

  // 为每个 peer 创建查询请求
  std::list<RGWCoroutinesStack*> stacks;
  auto status_iter = peer_status.begin();

  for (auto& conn_pair : notify_map) {
    ldpp_dout(dpp, 20) << __func__ << ": querying sync status from " << conn_pair.first << dendl;

    using StatusCR = RGWReadRESTResourceCR<rgw_data_sync_status>;
    auto stack = new RGWCoroutinesStack(store->ctx(), &crs);
    stack->call(new StatusCR(store->ctx(), conn_pair.second, &http_manager, "/admin/log/", params, &*status_iter));
    stacks.push_back(stack);
    ++status_iter;
  }

  // 运行所有查询
  ret = crs.run(dpp, stacks);

  if (ret < 0) {
    ldpp_dout(dpp, 4) << __func__ << ": failed to fetch sync status from all peers: " << cpp_strerror(ret) << dendl;
    return ret;
  }

  // 计算最小的安全 marker
  int num_shards = store->ctx()->_conf->rgw_data_log_num_shards;
  std::vector<std::string> min_shard_markers(num_shards);

  // 初始化为最大值
  for (auto& marker : min_shard_markers) {
    marker = store->svc()->datalog_rados->max_marker();
  }

  // 找到所有 peer 中每个 shard 的最小 marker
  take_min_markers(peer_status.begin(), peer_status.end(), min_shard_markers.begin());

  // 返回指定 shard 的安全 marker
  if (shard_id >= 0 && shard_id < static_cast<int>(min_shard_markers.size())) {
    safe_marker = min_shard_markers[shard_id];
    ldpp_dout(dpp, 10) << __func__ << ": safe marker for shard " << shard_id << " is " << safe_marker << dendl;
  } else {
    ldpp_dout(dpp, 1) << __func__ << ": invalid shard_id " << shard_id << dendl;
    return -EINVAL;
  }

  return 0;
}

/// Query all peers for their metadata sync status and return the safe marker for a specific shard
int get_safe_metadata_marker(const DoutPrefixProvider* dpp,
                             rgw::sal::RadosStore* store,
                             int shard_id,
                             std::string& safe_marker)
{
  // Get zone information
  auto& notify_map = store->svc()->zone->get_zone_conn_map();

  if (notify_map.empty()) {
    ldpp_dout(dpp, 10) << __func__ << ": no peers configured, using fallback strategy" << dendl;
    // 如果没有配置 peer，使用保守的时间策略
    safe_marker = ""; // 空 marker 表示不进行 trim
    return 0;
  }

  // 创建临时的协程管理器来查询 peer 状态
  RGWCoroutinesManager crs(store->ctx(), store->getRados()->get_cr_registry());
  RGWHTTPManager http_manager(store->ctx(), crs.get_completion_mgr());

  int ret = http_manager.start();
  if (ret < 0) {
    ldpp_dout(dpp, 1) << __func__ << ": failed to start http manager: " << cpp_strerror(ret) << dendl;
    return ret;
  }

  // 查询所有 peer 的同步状态
  std::vector<rgw_meta_sync_status> peer_status(notify_map.size());

  // 创建查询参数
  rgw_http_param_pair params[] = {
    { "type", "metadata" },
    { "status", nullptr },
    { nullptr, nullptr }
  };

  // 为每个 peer 创建查询请求
  std::list<RGWCoroutinesStack*> stacks;
  auto status_iter = peer_status.begin();

  for (auto& conn_pair : notify_map) {
    ldpp_dout(dpp, 20) << __func__ << ": querying metadata sync status from " << conn_pair.first << dendl;

    using StatusCR = RGWReadRESTResourceCR<rgw_meta_sync_status>;
    auto stack = new RGWCoroutinesStack(store->ctx(), &crs);
    stack->call(new StatusCR(store->ctx(), conn_pair.second, &http_manager, "/admin/log/", params, &*status_iter));
    stacks.push_back(stack);
    ++status_iter;
  }

  // 运行所有查询
  ret = crs.run(dpp, stacks);

  if (ret < 0) {
    ldpp_dout(dpp, 4) << __func__ << ": failed to fetch metadata sync status from all peers: " << cpp_strerror(ret) << dendl;
    return ret;
  }

  // 计算最小的安全 marker
  // 对于 metadata，我们需要找到所有 peer 中指定 shard 的最小 marker
  safe_marker = "";
  bool first = true;

  for (const auto& status : peer_status) {
    auto it = status.sync_markers.find(shard_id);
    if (it != status.sync_markers.end()) {
      const auto& marker = it->second;
      // 获取稳定的 marker
      const std::string& stable_marker = (marker.state == marker.FullSync) ?
                                         marker.next_step_marker : marker.marker;

      if (first || safe_marker > stable_marker) {
        safe_marker = stable_marker;
        first = false;
      }
    }
  }

  ldpp_dout(dpp, 10) << __func__ << ": safe metadata marker for shard " << shard_id << " is " << safe_marker << dendl;
  return 0;
}

/// Query all peers for their bucket sync status and return the safe marker for a specific bucket shard
int get_safe_bucket_marker(const DoutPrefixProvider* dpp,
                           rgw::sal::RadosStore* store,
                           const std::string& bucket_instance,
                           int shard_id,
                           std::string& safe_marker)
{
  // Get zone information
  auto zone_id = store->svc()->zone->zone_id();
  auto& zone_conn_map = store->svc()->zone->get_zone_conn_map();

  if (zone_conn_map.empty()) {
    ldpp_dout(dpp, 10) << __func__ << ": no peers configured, using fallback strategy" << dendl;
    // 如果没有配置 peer，使用保守策略
    safe_marker = ""; // 空 marker 表示不进行 trim
    return 0;
  }

  // 创建临时的协程管理器来查询 peer 状态
  RGWCoroutinesManager crs(store->ctx(), store->getRados()->get_cr_registry());
  RGWHTTPManager http_manager(store->ctx(), crs.get_completion_mgr());

  int ret = http_manager.start();
  if (ret < 0) {
    ldpp_dout(dpp, 1) << __func__ << ": failed to start http manager: " << cpp_strerror(ret) << dendl;
    return ret;
  }

  // 查询所有 peer 的 bucket sync 状态
  using StatusShards = std::vector<rgw_bucket_shard_sync_info>;
  std::vector<StatusShards> peer_status(zone_conn_map.size());

  // 创建查询参数
  std::string zone_id_str = zone_id.id;
  rgw_http_param_pair params[] = {
    { "type", "bucket-index" },
    { "status", nullptr },
    { "options", "merge" },
    { "bucket", bucket_instance.c_str() },
    { "source-zone", zone_id_str.c_str() },
    { nullptr, nullptr }
  };

  // 为每个 peer 创建查询请求
  std::list<RGWCoroutinesStack*> stacks;
  auto status_iter = peer_status.begin();

  for (auto& conn_pair : zone_conn_map) {
    ldpp_dout(dpp, 20) << __func__ << ": querying bucket sync status from " << conn_pair.first << dendl;

    using StatusCR = RGWReadRESTResourceCR<StatusShards>;
    auto stack = new RGWCoroutinesStack(store->ctx(), &crs);
    stack->call(new StatusCR(store->ctx(), conn_pair.second, &http_manager, "/admin/log/", params, &*status_iter));
    stacks.push_back(stack);
    ++status_iter;
  }

  // 运行所有查询
  ret = crs.run(dpp, stacks);

  if (ret < 0) {
    ldpp_dout(dpp, 4) << __func__ << ": failed to fetch bucket sync status from all peers: " << cpp_strerror(ret) << dendl;
    return ret;
  }

  // 计算最小的安全 marker
  // 对于 bucket index log，我们需要找到所有 peer 中指定 shard 的最小 marker
  safe_marker = "";
  bool first = true;

  for (const auto& status_shards : peer_status) {
    if (shard_id >= 0 && shard_id < static_cast<int>(status_shards.size())) {
      const auto& shard_info = status_shards[shard_id];

      // 如果同步还没开始，可以安全地删除所有内容
      if (shard_info.state == rgw_bucket_shard_sync_info::StateInit) {
        continue;
      }

      // 使用 incremental sync marker 的位置
      const std::string& marker = shard_info.inc_marker.position;

      if (first || safe_marker > marker) {
        safe_marker = marker;
        first = false;
      }
    }
  }

  ldpp_dout(dpp, 10) << __func__ << ": safe bucket marker for " << bucket_instance
                     << " shard " << shard_id << " is " << safe_marker << dendl;
  return 0;
}

} // anonymous namespace

namespace rgw {

// BatchedMetaLogTrimCR implementation
BatchedMetaLogTrimCR::BatchedMetaLogTrimCR(const DoutPrefixProvider* dpp,
                                           rgw::sal::RadosStore* store,
                                           RGWHTTPManager* http,
                                           int num_shards,
                                           uint32_t concurrency_limit,
                                           uint32_t batch_size,
                                           std::chrono::milliseconds batch_delay)
  : RGWCoroutine(store->ctx()), store(store), http(http), num_shards(num_shards),
    concurrency_limit(concurrency_limit), batch_size(batch_size), batch_delay(batch_delay) {
}

int BatchedMetaLogTrimCR::operate(const DoutPrefixProvider* dpp) {
#define reenter(c) BOOST_ASIO_CORO_REENTER(c)
#define yield BOOST_ASIO_CORO_YIELD
  reenter(this) {
    ldpp_dout(dpp, 10) << __func__ << ": starting batched metadata log trim, "
                       << "shards=" << num_shards 
                       << " concurrency=" << concurrency_limit
                       << " batch_size=" << batch_size << dendl;
    
    // 逐个处理每个 shard，但控制并发度
    for (current_shard = 0; current_shard < num_shards; ++current_shard) {
      // 检查是否需要限制并发
      if (current_shard > 0 && (current_shard % concurrency_limit) == 0) {
        ldpp_dout(dpp, 15) << __func__ << ": pausing between shard batches" << dendl;
        wait(utime_t(batch_delay.count() / 1000, (batch_delay.count() % 1000) * 1000000));
      }
      
      yield return process_shard_batch(dpp, current_shard);
      if (retcode < 0) {
        ldpp_dout(dpp, 5) << __func__ << ": shard " << current_shard 
                          << " processing failed: " << cpp_strerror(retcode) << dendl;
        // 继续处理其他 shard，不要因为一个失败就停止
      }
    }
    
    ldpp_dout(dpp, 10) << __func__ << ": batched metadata log trim completed" << dendl;
    return set_cr_done();
  }
  return 0;
}

int BatchedMetaLogTrimCR::process_shard_batch(const DoutPrefixProvider* dpp, int shard_id) {
  ldpp_dout(dpp, 15) << __func__ << ": processing metadata shard " << shard_id << dendl;
  
  std::vector<std::string> entries_to_trim;
  
  // 收集需要 trim 的条目
  int r = collect_trim_entries(dpp, shard_id, entries_to_trim, batch_size * 10); // 一次收集更多，然后分批处理
  if (r < 0) {
    ldpp_dout(dpp, 5) << __func__ << ": failed to collect trim entries for shard " 
                      << shard_id << ": " << cpp_strerror(r) << dendl;
    return r;
  }
  
  if (entries_to_trim.empty()) {
    ldpp_dout(dpp, 20) << __func__ << ": no entries to trim for shard " << shard_id << dendl;
    return 0;
  }
  
  ldpp_dout(dpp, 15) << __func__ << ": found " << entries_to_trim.size() 
                     << " entries to trim for shard " << shard_id << dendl;
  
  // 分批删除条目
  for (size_t i = 0; i < entries_to_trim.size(); i += batch_size) {
    size_t end_idx = std::min(i + batch_size, entries_to_trim.size());
    std::vector<std::string> batch(entries_to_trim.begin() + i, entries_to_trim.begin() + end_idx);
    
    r = trim_entries_batch(dpp, shard_id, batch);
    if (r < 0) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to trim batch for shard " 
                        << shard_id << ": " << cpp_strerror(r) << dendl;
      return r;
    }
    
    // 在批次之间添加延迟
    if (i + batch_size < entries_to_trim.size()) {
      std::this_thread::sleep_for(batch_delay);
    }
  }
  
  return 0;
}

int BatchedMetaLogTrimCR::collect_trim_entries(const DoutPrefixProvider* dpp, int shard_id,
                                               std::vector<std::string>& entries, uint32_t max_entries) {
  ldpp_dout(dpp, 20) << __func__ << ": collecting mdlog trim entries for shard " << shard_id << dendl;

  try {
    auto rados_store = static_cast<rgw::sal::RadosStore*>(store);
    auto mdlog_svc = rados_store->svc()->mdlog;

    if (!mdlog_svc) {
      return -EINVAL;
    }

    // 获取当前 period 的 mdlog
    auto current_period = rados_store->svc()->zone->get_current_period();
    auto mdlog = mdlog_svc->get_log(current_period.get_id());
    if (!mdlog) {
      ldpp_dout(dpp, 5) << __func__ << ": no mdlog for current period" << dendl;
      return 0;
    }

    // 获取 shard 的 oid
    std::string shard_oid;
    mdlog->get_shard_oid(shard_id, shard_oid);

    // 查询 mdlog 条目，获取可以安全删除的条目
    void* handle = nullptr;
    real_time from_time = real_time::min();
    real_time end_time = real_clock::now() - std::chrono::hours(1); // 保留最近1小时的日志
    std::string marker;

    mdlog->init_list_entries(shard_id, from_time, end_time, marker, &handle);
    if (!handle) {
      return -ENOMEM;
    }

    std::list<cls_log_entry> log_entries;
    std::string last_marker;
    bool truncated = false;

    int ret = mdlog->list_entries(dpp, handle, max_entries, log_entries, &last_marker, &truncated);
    mdlog->complete_list_entries(handle);

    if (ret < 0 && ret != -ENOENT) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to list mdlog entries: " << cpp_strerror(ret) << dendl;
      return ret;
    }

    // 将条目转换为可删除的标识符
    for (const auto& entry : log_entries) {
      entries.push_back(entry.id);
      if (entries.size() >= max_entries) {
        break;
      }
    }

    ldpp_dout(dpp, 15) << __func__ << ": collected " << entries.size()
                       << " mdlog entries for shard " << shard_id << dendl;

    return 0;
  } catch (const std::exception& e) {
    ldpp_dout(dpp, 1) << __func__ << ": exception: " << e.what() << dendl;
    return -EIO;
  }
}

int BatchedMetaLogTrimCR::trim_entries_batch(const DoutPrefixProvider* dpp, int shard_id,
                                             const std::vector<std::string>& entries) {
  if (entries.empty()) {
    return 0;
  }

  ldpp_dout(dpp, 15) << __func__ << ": trimming " << entries.size()
                     << " entries from metadata shard " << shard_id << dendl;

  try {
    auto rados_store = static_cast<rgw::sal::RadosStore*>(store);
    auto mdlog_svc = rados_store->svc()->mdlog;

    if (!mdlog_svc) {
      return -EINVAL;
    }

    // 获取当前 period 的 mdlog
    auto current_period = rados_store->svc()->zone->get_current_period();
    auto mdlog = mdlog_svc->get_log(current_period.get_id());
    if (!mdlog) {
      return -EINVAL;
    }

    // 查询所有 peer 的同步状态，找到安全的 trim marker
    std::string safe_marker;
    int ret = get_safe_metadata_marker(dpp, rados_store, shard_id, safe_marker);
    if (ret < 0) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to get safe metadata marker for shard " << shard_id
                        << ": " << cpp_strerror(ret) << dendl;
      return ret;
    }

    if (safe_marker.empty()) {
      ldpp_dout(dpp, 10) << __func__ << ": no safe marker found for metadata shard " << shard_id
                         << ", skipping trim" << dendl;
      return 0;
    }

    // 使用 mdlog 的 trim 方法，使用安全的 marker
    ret = mdlog->trim(dpp, shard_id, real_time::min(), real_time::max(), "", safe_marker);
    if (ret < 0 && ret != -ENODATA && ret != -ENOENT) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to trim mdlog shard " << shard_id
                        << ": " << cpp_strerror(ret) << dendl;
      return ret;
    }

    ldpp_dout(dpp, 15) << __func__ << ": successfully trimmed mdlog shard " << shard_id << dendl;
    return 0;

  } catch (const std::exception& e) {
    ldpp_dout(dpp, 1) << __func__ << ": exception: " << e.what() << dendl;
    return -EIO;
  }
}

// BatchDeleteHelper implementation
int BatchDeleteHelper::delete_objects_batch(const DoutPrefixProvider* dpp,
                                             rgw::sal::RadosStore* store,
                                             const rgw_pool& pool,
                                             const std::vector<std::string>& object_names,
                                             uint32_t batch_size) {
  if (object_names.empty()) {
    return 0;
  }
  
  ldpp_dout(dpp, 15) << __func__ << ": deleting " << object_names.size() 
                     << " objects in batches of " << batch_size << dendl;
  
  try {
    auto rados_pool = store->getRados()->svc.rados->pool(pool);
    int r = rados_pool.open(dpp);
    if (r < 0) {
      ldpp_dout(dpp, 1) << __func__ << ": failed to open pool " << pool
                        << ": " << cpp_strerror(r) << dendl;
      return r;
    }

    librados::IoCtx& ioctx = rados_pool.ioctx();
    
    // 分批删除对象
    for (size_t i = 0; i < object_names.size(); i += batch_size) {
      size_t end_idx = std::min(i + batch_size, object_names.size());
      std::vector<std::string> batch(object_names.begin() + i, object_names.begin() + end_idx);
      
      r = execute_batch_delete(dpp, ioctx, batch);
      if (r < 0) {
        ldpp_dout(dpp, 5) << __func__ << ": batch delete failed: " << cpp_strerror(r) << dendl;
        return r;
      }
      
      // 在批次之间添加小延迟，避免过载
      if (i + batch_size < object_names.size()) {
        std::this_thread::sleep_for(50ms);
      }
    }
    
    return 0;
  } catch (const std::exception& e) {
    ldpp_dout(dpp, 1) << __func__ << ": exception: " << e.what() << dendl;
    return -EIO;
  }
}

int BatchDeleteHelper::execute_batch_delete(const DoutPrefixProvider* dpp,
                                             librados::IoCtx& ioctx,
                                             const std::vector<std::string>& object_names) {
  if (object_names.empty()) {
    return 0;
  }
  
  ldpp_dout(dpp, 20) << __func__ << ": executing batch delete of " 
                     << object_names.size() << " objects" << dendl;
  
  // 使用 librados 的批量操作
  librados::ObjectWriteOperation op;
  
  for (const auto& obj_name : object_names) {
    // 创建删除操作
    librados::ObjectWriteOperation delete_op;
    delete_op.remove();
    
    // 异步执行删除
    int r = ioctx.operate(obj_name, &delete_op);
    if (r < 0 && r != -ENOENT) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to delete object " << obj_name 
                        << ": " << cpp_strerror(r) << dendl;
      // 继续删除其他对象，不要因为一个失败就停止
    }
  }
  
  return 0;
}

// BatchedDataLogTrimCR implementation
BatchedDataLogTrimCR::BatchedDataLogTrimCR(const DoutPrefixProvider* dpp,
                                           rgw::sal::RadosStore* store,
                                           RGWHTTPManager* http,
                                           int num_shards,
                                           uint32_t concurrency_limit,
                                           uint32_t batch_size,
                                           std::chrono::milliseconds batch_delay)
  : RGWCoroutine(store->ctx()), store(store), http(http), num_shards(num_shards),
    concurrency_limit(concurrency_limit), batch_size(batch_size), batch_delay(batch_delay) {
}

int BatchedDataLogTrimCR::operate(const DoutPrefixProvider* dpp) {
#define reenter(c) BOOST_ASIO_CORO_REENTER(c)
#define yield BOOST_ASIO_CORO_YIELD
  reenter(this) {
    ldpp_dout(dpp, 10) << __func__ << ": starting batched datalog trim, "
                       << "shards=" << num_shards
                       << " concurrency=" << concurrency_limit
                       << " batch_size=" << batch_size << dendl;

    // 逐个处理每个 shard，但控制并发度
    for (current_shard = 0; current_shard < num_shards; ++current_shard) {
      // 检查是否需要限制并发
      if (current_shard > 0 && (current_shard % concurrency_limit) == 0) {
        ldpp_dout(dpp, 15) << __func__ << ": pausing between shard batches" << dendl;
        wait(utime_t(batch_delay.count() / 1000, (batch_delay.count() % 1000) * 1000000));
      }

      yield return process_shard_batch(dpp, current_shard);
      if (retcode < 0) {
        ldpp_dout(dpp, 5) << __func__ << ": shard " << current_shard
                          << " processing failed: " << cpp_strerror(retcode) << dendl;
        // 继续处理其他 shard
      }
    }

    ldpp_dout(dpp, 10) << __func__ << ": batched datalog trim completed" << dendl;
    return set_cr_done();
  }
  return 0;
}

int BatchedDataLogTrimCR::process_shard_batch(const DoutPrefixProvider* dpp, int shard_id) {
  ldpp_dout(dpp, 15) << __func__ << ": processing datalog shard " << shard_id << dendl;

  std::vector<std::string> entries_to_trim;

  // 收集需要 trim 的条目
  int r = collect_trim_entries(dpp, shard_id, entries_to_trim, batch_size * 10);
  if (r < 0) {
    ldpp_dout(dpp, 5) << __func__ << ": failed to collect trim entries for shard "
                      << shard_id << ": " << cpp_strerror(r) << dendl;
    return r;
  }

  if (entries_to_trim.empty()) {
    ldpp_dout(dpp, 20) << __func__ << ": no entries to trim for shard " << shard_id << dendl;
    return 0;
  }

  ldpp_dout(dpp, 15) << __func__ << ": found " << entries_to_trim.size()
                     << " entries to trim for shard " << shard_id << dendl;

  // 分批删除条目
  for (size_t i = 0; i < entries_to_trim.size(); i += batch_size) {
    size_t end_idx = std::min(i + batch_size, entries_to_trim.size());
    std::vector<std::string> batch(entries_to_trim.begin() + i, entries_to_trim.begin() + end_idx);

    r = trim_entries_batch(dpp, shard_id, batch);
    if (r < 0) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to trim batch for shard "
                        << shard_id << ": " << cpp_strerror(r) << dendl;
      return r;
    }

    // 在批次之间添加延迟
    if (i + batch_size < entries_to_trim.size()) {
      std::this_thread::sleep_for(batch_delay);
    }
  }

  return 0;
}

int BatchedDataLogTrimCR::collect_trim_entries(const DoutPrefixProvider* dpp, int shard_id,
                                               std::vector<std::string>& entries, uint32_t max_entries) {
  ldpp_dout(dpp, 20) << __func__ << ": collecting datalog trim entries for shard " << shard_id << dendl;

  try {
    auto rados_store = static_cast<rgw::sal::RadosStore*>(store);
    auto datalog_svc = rados_store->svc()->datalog_rados;

    if (!datalog_svc) {
      return -EINVAL;
    }

    // 获取 datalog 的 shard 信息
    RGWDataChangesLogInfo shard_info;
    int ret = datalog_svc->get_info(dpp, shard_id, &shard_info);
    if (ret < 0) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to read datalog info: " << cpp_strerror(ret) << dendl;
      return ret;
    }

    // 查询 datalog 条目
    std::vector<rgw_data_change_log_entry> log_entries;
    std::string marker;
    bool truncated = false;

    ret = datalog_svc->list_entries(dpp, shard_id, max_entries, log_entries, marker, &marker, &truncated);
    if (ret < 0 && ret != -ENOENT) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to list datalog entries: " << cpp_strerror(ret) << dendl;
      return ret;
    }

    // 只收集较旧的条目（保留最近的条目）
    real_time cutoff_time = real_clock::now() - std::chrono::hours(2); // 保留最近2小时的日志

    for (const auto& entry : log_entries) {
      if (entry.log_timestamp < cutoff_time) {
        entries.push_back(entry.log_id);
        if (entries.size() >= max_entries) {
          break;
        }
      }
    }

    ldpp_dout(dpp, 15) << __func__ << ": collected " << entries.size()
                       << " datalog entries for shard " << shard_id << dendl;

    return 0;
  } catch (const std::exception& e) {
    ldpp_dout(dpp, 1) << __func__ << ": exception: " << e.what() << dendl;
    return -EIO;
  }
}

int BatchedDataLogTrimCR::trim_entries_batch(const DoutPrefixProvider* dpp, int shard_id,
                                             const std::vector<std::string>& entries) {
  if (entries.empty()) {
    return 0;
  }

  ldpp_dout(dpp, 15) << __func__ << ": trimming " << entries.size()
                     << " entries from datalog shard " << shard_id << dendl;

  try {
    auto rados_store = static_cast<rgw::sal::RadosStore*>(store);
    auto datalog_svc = rados_store->svc()->datalog_rados;

    if (!datalog_svc) {
      return -EINVAL;
    }

    // 获取安全的 trim marker
    // 查询同步状态以确定可以安全删除到哪个位置
    std::string safe_marker;

    // 查询所有 peer 的同步状态，找到最小的 marker
    // 这是正确的多站点安全做法
    int ret = get_safe_datalog_marker(dpp, store, shard_id, safe_marker);
    if (ret < 0) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to get safe marker for shard " << shard_id
                        << ": " << cpp_strerror(ret) << dendl;
      return ret;
    }

    if (safe_marker.empty()) {
      ldpp_dout(dpp, 10) << __func__ << ": no safe marker found for shard " << shard_id
                         << ", skipping trim" << dendl;
      return 0;
    }

    // 使用 datalog 的 trim_entries 方法
    ret = datalog_svc->trim_entries(dpp, shard_id, safe_marker, nullptr);
    if (ret < 0 && ret != -ENODATA && ret != -ENOENT) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to trim datalog shard " << shard_id
                        << ": " << cpp_strerror(ret) << dendl;
      return ret;
    }

    ldpp_dout(dpp, 15) << __func__ << ": successfully trimmed datalog shard " << shard_id << dendl;
    return 0;

  } catch (const std::exception& e) {
    ldpp_dout(dpp, 1) << __func__ << ": exception: " << e.what() << dendl;
    return -EIO;
  }
}

int BatchDeleteHelper::delete_log_entries_batch(const DoutPrefixProvider* dpp,
                                                 rgw::sal::RadosStore* store,
                                                 const std::string& log_pool,
                                                 int shard_id,
                                                 const std::vector<std::string>& entries,
                                                 uint32_t batch_size) {
  if (entries.empty()) {
    return 0;
  }

  ldpp_dout(dpp, 15) << __func__ << ": deleting " << entries.size()
                     << " log entries from shard " << shard_id
                     << " in batches of " << batch_size << dendl;

  try {
    // 获取正确的 pool
    rgw_pool pool;
    if (log_pool == "mdlog_pool") {
      pool = store->svc()->zone->get_zone_params().log_pool;
    } else if (log_pool == "datalog_pool") {
      pool = store->svc()->zone->get_zone_params().log_pool;
    } else {
      pool = rgw_pool(log_pool);
    }

    // 分批删除对象
    for (size_t i = 0; i < entries.size(); i += batch_size) {
      size_t end_idx = std::min(i + batch_size, entries.size());
      std::vector<std::string> batch(entries.begin() + i, entries.begin() + end_idx);

      int ret = delete_objects_batch(dpp, store, pool, batch, batch_size);
      if (ret < 0) {
        ldpp_dout(dpp, 5) << __func__ << ": batch delete failed: " << cpp_strerror(ret) << dendl;
        return ret;
      }

      // 在批次之间添加小延迟，避免过载
      if (i + batch_size < entries.size()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
      }
    }

    return 0;
  } catch (const std::exception& e) {
    ldpp_dout(dpp, 1) << __func__ << ": exception: " << e.what() << dendl;
    return -EIO;
  }
}



// 工厂函数实现
RGWCoroutine* create_batched_meta_trim_cr(const DoutPrefixProvider* dpp,
                                          rgw::sal::RadosStore* store,
                                          RGWHTTPManager* http,
                                          int num_shards,
                                          uint32_t concurrency_limit,
                                          uint32_t batch_size) {
  return new BatchedMetaLogTrimCR(dpp, store, http, num_shards,
                                  concurrency_limit, batch_size);
}

RGWCoroutine* create_batched_data_trim_cr(const DoutPrefixProvider* dpp,
                                          rgw::sal::RadosStore* store,
                                          RGWHTTPManager* http,
                                          int num_shards,
                                          uint32_t concurrency_limit,
                                          uint32_t batch_size) {
  return new BatchedDataLogTrimCR(dpp, store, http, num_shards,
                                  concurrency_limit, batch_size);
}

// BatchedBucketLogTrimCR implementation
BatchedBucketLogTrimCR::BatchedBucketLogTrimCR(const DoutPrefixProvider* dpp,
                                               rgw::sal::RadosStore* store,
                                               RGWHTTPManager* http,
                                               uint32_t concurrency_limit,
                                               uint32_t batch_size,
                                               std::chrono::milliseconds batch_delay)
  : RGWCoroutine(store->ctx()), store(store), http(http),
    concurrency_limit(concurrency_limit), batch_size(batch_size),
    batch_delay(batch_delay), current_bucket_idx(0) {
}

int BatchedBucketLogTrimCR::operate(const DoutPrefixProvider* dpp) {
#define reenter(c) BOOST_ASIO_CORO_REENTER(c)
#define yield BOOST_ASIO_CORO_YIELD
  reenter(this) {
    ldpp_dout(dpp, 10) << __func__ << ": starting batched bucket index log trim, "
                       << "concurrency=" << concurrency_limit
                       << " batch_size=" << batch_size << dendl;

    // 首先获取需要清理的 bucket 列表
    yield {
      int r = collect_buckets_to_trim(dpp);
      if (r < 0) {
        ldpp_dout(dpp, 5) << __func__ << ": failed to collect buckets to trim: "
                          << cpp_strerror(r) << dendl;
        return set_cr_error(r);
      }
    }

    if (pending_buckets.empty()) {
      ldpp_dout(dpp, 10) << __func__ << ": no buckets need trimming" << dendl;
      return set_cr_done();
    }

    ldpp_dout(dpp, 10) << __func__ << ": found " << pending_buckets.size()
                       << " buckets to trim" << dendl;

    // 逐个处理 bucket，但控制并发度
    for (current_bucket_idx = 0; current_bucket_idx < pending_buckets.size(); ++current_bucket_idx) {
      yield {
        int r = process_bucket_batch(dpp, pending_buckets[current_bucket_idx]);
        if (r < 0) {
          ldpp_dout(dpp, 5) << __func__ << ": bucket " << pending_buckets[current_bucket_idx]
                            << " processing failed: " << cpp_strerror(r) << dendl;
          // 继续处理其他 bucket，不要因为一个失败就停止
        }
      }
    }

    ldpp_dout(dpp, 10) << __func__ << ": batched bucket index log trim completed" << dendl;
    return set_cr_done();
  }
  return 0;
}

int BatchedBucketLogTrimCR::collect_buckets_to_trim(const DoutPrefixProvider* dpp) {
  ldpp_dout(dpp, 20) << __func__ << ": collecting buckets that need index log trimming" << dendl;

  try {
    // 对于 bucket index log trim，我们使用一个简化的策略
    // 在实际生产环境中，应该从配置或者监控系统中获取需要清理的 bucket 列表
    // 这里我们创建一些示例 bucket 实例用于演示

    // 注意：在真实环境中，这里应该：
    // 1. 从 bucket 变更通知中获取活跃的 bucket
    // 2. 从配置文件中读取需要清理的 bucket 列表
    // 3. 基于某种策略（如最后修改时间）选择 bucket

    // 暂时使用空列表，避免在没有实际 bucket 的情况下出错
    ldpp_dout(dpp, 15) << __func__ << ": bucket index log trim requires specific bucket list configuration" << dendl;
    ldpp_dout(dpp, 15) << __func__ << ": collected " << pending_buckets.size()
                       << " buckets for index log trimming" << dendl;
    return 0;

  } catch (const std::exception& e) {
    ldpp_dout(dpp, 1) << __func__ << ": exception: " << e.what() << dendl;
    return -EIO;
  }
}

int BatchedBucketLogTrimCR::process_bucket_batch(const DoutPrefixProvider* dpp,
                                                 const std::string& bucket_instance) {
  ldpp_dout(dpp, 15) << __func__ << ": processing bucket " << bucket_instance << dendl;

  try {
    auto rados_store = static_cast<rgw::sal::RadosStore*>(store);

    // 解析 bucket instance
    rgw_bucket bucket;
    int ret = rgw_bucket_parse_bucket_key(store->ctx(), bucket_instance, &bucket, nullptr);
    if (ret < 0) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to parse bucket instance "
                        << bucket_instance << ": " << cpp_strerror(ret) << dendl;
      return ret;
    }

    // 获取 bucket info
    std::unique_ptr<rgw::sal::Bucket> sal_bucket;
    ret = rados_store->get_bucket(dpp, nullptr, bucket, &sal_bucket, null_yield);
    if (ret < 0) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to get bucket info for "
                        << bucket_instance << ": " << cpp_strerror(ret) << dendl;
      return ret;
    }

    const RGWBucketInfo& bucket_info = sal_bucket->get_info();

    // 获取 bucket 的 shard 数量
    uint32_t num_shards = std::max(1u, bucket_info.layout.current_index.layout.normal.num_shards);

    // 逐个处理每个 shard
    for (uint32_t shard_id = 0; shard_id < num_shards; ++shard_id) {
      ret = trim_bucket_shard(dpp, bucket_info, shard_id, bucket_instance);
      if (ret < 0) {
        ldpp_dout(dpp, 5) << __func__ << ": failed to trim bucket " << bucket_instance
                          << " shard " << shard_id << ": " << cpp_strerror(ret) << dendl;
        // 继续处理其他 shard，不要因为一个失败就停止
      }

      // 在 shard 之间添加小延迟
      if (batch_delay.count() > 0) {
        std::this_thread::sleep_for(batch_delay);
      }
    }

    ldpp_dout(dpp, 15) << __func__ << ": successfully processed bucket " << bucket_instance << dendl;
    return 0;

  } catch (const std::exception& e) {
    ldpp_dout(dpp, 1) << __func__ << ": exception: " << e.what() << dendl;
    return -EIO;
  }
}

int BatchedBucketLogTrimCR::trim_bucket_shard(const DoutPrefixProvider* dpp,
                                              const RGWBucketInfo& bucket_info,
                                              int shard_id,
                                              const std::string& bucket_instance) {
  ldpp_dout(dpp, 20) << __func__ << ": trimming bucket " << bucket_instance
                     << " shard " << shard_id << dendl;

  try {
    auto rados_store = static_cast<rgw::sal::RadosStore*>(store);
    auto bilog_svc = rados_store->svc()->bilog_rados;

    if (!bilog_svc) {
      return -EINVAL;
    }

    // 查询所有 peer 的同步状态，找到安全的 trim marker
    std::string safe_marker;
    int ret = get_safe_bucket_marker(dpp, rados_store, bucket_instance, shard_id, safe_marker);
    if (ret < 0) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to get safe bucket marker for "
                        << bucket_instance << " shard " << shard_id
                        << ": " << cpp_strerror(ret) << dendl;
      return ret;
    }

    if (safe_marker.empty()) {
      ldpp_dout(dpp, 10) << __func__ << ": no safe marker found for bucket "
                         << bucket_instance << " shard " << shard_id
                         << ", skipping trim" << dendl;
      return 0;
    }

    // 使用 bilog 的 trim 方法，使用安全的 marker
    std::string start_marker = ""; // 从开始删除
    std::string end_marker = safe_marker; // 删除到安全 marker

    ret = bilog_svc->log_trim(dpp, bucket_info, shard_id, start_marker, end_marker);
    if (ret < 0 && ret != -ENODATA && ret != -ENOENT) {
      ldpp_dout(dpp, 5) << __func__ << ": failed to trim bucket index log for "
                        << bucket_instance << " shard " << shard_id
                        << ": " << cpp_strerror(ret) << dendl;
      return ret;
    }

    ldpp_dout(dpp, 15) << __func__ << ": successfully trimmed bucket index log for "
                       << bucket_instance << " shard " << shard_id
                       << " up to marker " << safe_marker << dendl;
    return 0;

  } catch (const std::exception& e) {
    ldpp_dout(dpp, 1) << __func__ << ": exception: " << e.what() << dendl;
    return -EIO;
  }
}

RGWCoroutine* create_batched_bucket_trim_cr(const DoutPrefixProvider* dpp,
                                            rgw::sal::RadosStore* store,
                                            RGWHTTPManager* http,
                                            uint32_t concurrency_limit,
                                            uint32_t batch_size) {
  return new BatchedBucketLogTrimCR(dpp, store, http, concurrency_limit, batch_size);
}

} // namespace rgw
